<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\LeaveRequestRepositoryInterface;
use App\Models\LeaveRequest;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class LeaveRequestRepository implements LeaveRequestRepositoryInterface
{
    /**
     * LeaveRequest model instance
     */
    private LeaveRequest $leaveRequestModel;

    /**
     * LeaveRequestRepository constructor.
     */
    public function __construct(LeaveRequest $leaveRequestModel)
    {
        $this->leaveRequestModel = $leaveRequestModel;
    }

    /**
     * Get all leave requests with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->leaveRequestModel->with(['teacher.user', 'approver', 'tasks'])
            ->when(! empty($filters['status']), function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->when(! empty($filters['teacher_id']), function ($query) use ($filters) {
                $query->where('teacher_id', $filters['teacher_id']);
            })
            ->when(! empty($filters['leave_type']), function ($query) use ($filters) {
                $query->where('leave_type', $filters['leave_type']);
            })
            ->when(! empty($filters['leave_date']), function ($query) use ($filters) {
                $query->where('leave_date', '=', $filters['leave_date']);
            })
            ->when(! empty($filters['search']), function ($query) use ($filters) {
                $searchTerm = '%'.$filters['search'].'%';
                $query->where(function ($query) use ($searchTerm) {
                    $query->where('reason', 'like', $searchTerm)
                        ->orWhere('leave_type', 'like', $searchTerm)
                        ->orWhereHas('teacher.user', function ($query) use ($searchTerm) {
                            $query->where('name', 'like', $searchTerm);
                        });
                });
            })
            ->when(! empty($filters['date_from']), function ($query) use ($filters) {
                $query->where('leave_date', '>=', $filters['date_from']);
            })
            ->when(! empty($filters['date_to']), function ($query) use ($filters) {
                $query->where('leave_date', '<=', $filters['date_to']);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Create a new leave request.
     */
    public function create(array $data): LeaveRequest
    {
        $leaveRequest = $this->leaveRequestModel->newInstance();
        $leaveRequest->fill($data);
        $leaveRequest->save();

        return $leaveRequest->load(['teacher.user', 'tasks']);
    }

    /**
     * Update an existing leave request.
     */
    public function update(int $id, array $data): bool
    {
        $leaveRequest = $this->findById($id);
        $leaveRequest->fill($data);
        
        return $leaveRequest->save();
    }

    /**
     * Delete a leave request.
     */
    public function delete(int $id): bool
    {
        $leaveRequest = $this->findById($id);
        
        return $leaveRequest->delete();
    }

    /**
     * Find a leave request by ID.
     */
    public function findById(int $id): LeaveRequest
    {
        return $this->leaveRequestModel->with(['teacher.user', 'approver', 'tasks'])->findOrFail($id);
    }

    /**
     * Find overlapping leave requests for a teacher.
     */
    public function findOverlappingLeaves(int $teacherId, string $leaveDate, ?int $excludeId = null): Collection
    {
        return $this->leaveRequestModel
            ->where('teacher_id', $teacherId)
            ->where('leave_date', $leaveDate)
            ->when($excludeId, function ($query, $excludeId) {
                return $query->where('id', '!=', $excludeId);
            })
            ->whereIn('status', ['pending', 'approved'])
            ->get();
    }

    /**
     * Get leave requests by teacher ID.
     */
    public function getByEmployeeId(int $teacherId, array $filters = []): Collection
    {
        return $this->leaveRequestModel->with(['teacher.user', 'approver', 'tasks'])
            ->where('teacher_id', $teacherId)
            ->when(! empty($filters['status']), function ($query) use ($filters) {
                $query->where('status', $filters['status']);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get leave requests by status.
     */
    public function getByStatus(string $status, array $filters = []): Collection
    {
        return $this->leaveRequestModel->with(['teacher.user'])
            ->where('status', $status)
            ->when(! empty($filters['teacher_id']), function ($query) use ($filters) {
                $query->where('teacher_id', $filters['teacher_id']);
            })
            ->when(! empty($filters['leave_type']), function ($query) use ($filters) {
                $query->where('leave_type', $filters['leave_type']);
            })
            ->when(! empty($filters['date_from']), function ($query) use ($filters) {
                $query->where('leave_date', '>=', $filters['date_from']);
            })
            ->when(! empty($filters['date_to']), function ($query) use ($filters) {
                $query->where('leave_date', '<=', $filters['date_to']);
            })
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Count total leave requests.
     */
    public function count(): int
    {
        return $this->leaveRequestModel->count();
    }
}
