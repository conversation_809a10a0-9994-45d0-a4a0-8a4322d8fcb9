<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\ProgramService;
use App\Services\StudentService;
use App\Services\SubjectService;
use App\Services\TeacherService;
use Illuminate\View\View;

final class AdminController extends Controller
{
    public function __construct(
        private readonly StudentService $studentService,
        private readonly TeacherService $teacherService,
        private readonly ProgramService $programService,
        private readonly SubjectService $subjectService
    ) {
    }

    /**
     * Display admin dashboard
     */
    public function index(): View
    {
        $stats = [
            'total_students' => $this->studentService->getTotalStudents(),
            'total_teachers' => $this->teacherService->getTotalTeachers(),
            'total_programs' => $this->programService->getTotalPrograms(),
            'total_subjects' => $this->subjectService->getTotalSubjects(),
        ];

        $programStats = $this->programService->getProgramsWithClassroomStats();

        $subjectsByProgram = [
            ['id' => 1, 'name' => 'Program Reguler', 'count' => 12, 'color' => 'primary'],
            ['id' => 2, 'name' => 'Program Unggulan', 'count' => 15, 'color' => 'success'],
            ['id' => 3, 'name' => 'Program Khusus', 'count' => 8, 'color' => 'info'],
            ['id' => 4, 'name' => 'Program Lainnya', 'count' => 5, 'color' => 'warning'],
        ];

        return view('admin.dashboard', compact(
            'stats',
            'programStats',
            'subjectsByProgram'
        ));
    }
}
