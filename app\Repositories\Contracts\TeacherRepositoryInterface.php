<?php

namespace App\Repositories\Contracts;

use App\Models\Teacher;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException; // Penting: Import ini
// Hapus import untuk App\Exceptions\NotFoundException, BusinessLogicException, DatabaseException
// Karena repository hanya melempar ModelNotFoundException atau generic Throwable

interface TeacherRepositoryInterface
{
    /**
     * Count total number of teachers.
     */
    public function count(): int;

    /**
     * Get all teachers with optional filters.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active teachers.
     */
    public function getAllActive(): Collection;

    /**
     * Find a teacher by ID.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function findById(int $id): Teacher;

    /**
     * Create a new teacher record.
     *
     * @param array $data Teacher data (excluding user details and role assignment handled by service)
     * @throws \Throwable Jika terjadi kesalahan pada level database
     */
    public function create(array $data): Teacher;

    /**
     * Update an existing teacher record.
     *
     * @param int $id Teacher ID
     * @param array $data Updated teacher data (excluding user details and role assignment handled by service)
     * @return bool True if update was successful, false otherwise.
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @throws \Throwable Jika terjadi kesalahan pada level database
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a teacher record.
     *
     * @param int $id Teacher ID
     * @return bool True if deletion was successful, false otherwise.
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     * @throws \Throwable Jika terjadi kesalahan pada level database
     */
    public function delete(int $id): bool;

    /**
     * Get available teachers based on filters.
     */
    public function getAvailableTeachers(array $filters): Collection;

    /**
     * Get teacher assignments.
     */
    public function getTeacherAssignments(int $teacherId): Collection;

    /**
     * Get teacher schedule for a specific date.
     */
    public function getTeacherSchedule(int $teacherId, string $date): Collection;

    /**
     * Check if teacher has active assignments.
     */
    public function hasActiveAssignments(int $teacherId): bool;

    /**
     * Get homeroom teachers.
     */
    public function getHomeroomTeachers(): Collection;

    // --- Metode yang dihapus dari antarmuka karena tidak lagi diimplementasikan di TeacherRepository: ---
    // public function createTeacherWithUser(array $data): Teacher; // Logika ini dipindahkan ke TeacherService
    // public function updateTeacherWithUser(int $id, array $data): bool; // Logika ini dipindahkan ke TeacherService
    // public function updateTeacherStatus(int $id, $status): bool; // Logika ini dipindahkan ke TeacherService (memanggil UserService)
    // public function findByUserId(int $userId): ?Teacher; // Tidak digunakan di alur utama, bisa ditambahkan jika ada kebutuhan spesifik
    // public function getAllWithRelations(array $relations, array $filters = []): Collection; // Lebih baik menggunakan eager loading di Service
}
