<?php

namespace App\Services;

use App\Repositories\Contracts\ShiftRepositoryInterface;
use App\Models\Shift;
use Illuminate\Database\Eloquent\Collection;

class ShiftService
{
    /**
     * The shift repository instance.
     */
    protected ShiftRepositoryInterface $shiftRepository;

    /**
     * Create a new ShiftService instance.
     */
    public function __construct(ShiftRepositoryInterface $shiftRepository)
    {
        $this->shiftRepository = $shiftRepository;
    }

    /**
     * Get all shifts with filtering.
     */
    public function getAllShifts(array $filters = []): Collection
    {
        return $this->shiftRepository->getAllShifts($filters);
    }

    /**
     * Get a shift by ID.
     */
    public function getShiftById(int $id): Shift
    {
        return $this->shiftRepository->getShiftById($id);
    }

    /**
     * Create a new shift.
     *
     * @throws \Exception
     */
    public function createShift(array $data): Shift
    {
        return $this->shiftRepository->createShift($data);
    }

    /**
     * Update an existing shift.
     *
     * @throws \Exception
     */
    public function updateShift(int $id, array $data): bool
    {
        return $this->shiftRepository->updateShift($id, $data);
    }

    /**
     * Delete a shift.
     *
     * @throws \Exception
     */
    public function deleteShift(int $id): bool
    {
        return $this->shiftRepository->deleteShift($id);
    }

    /**
     * Get all active shifts.
     */
    public function getAllActiveShifts(): Collection
    {
        return $this->shiftRepository->getAllActiveShifts();
    }

    /**
     * Get total number of shifts.
     */
    public function getTotalShifts(): int
    {
        return $this->shiftRepository->count();
    }
}

