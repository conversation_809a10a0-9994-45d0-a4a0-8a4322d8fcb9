<?php

namespace App\Services;

use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\DatabaseException;
use App\Exceptions\NotFoundException;
use App\Models\Student;
use Illuminate\Support\Facades\DB;
use Throwable;

class StudentRegistrationService
{
    /**
     * The student repository instance.
     */
    protected StudentRepositoryInterface $studentRepository;

    /**
     * The user repository instance.
     */
    protected UserRepositoryInterface $userRepository;

    /**
     * Create a new StudentRegistrationService instance.
     */
    public function __construct(
        StudentRepositoryInterface $studentRepository,
        UserRepositoryInterface $userRepository
    ) {
        $this->studentRepository = $studentRepository;
        $this->userRepository = $userRepository;
    }

    /**
     * Register a new student with user account.
     *
     * @param array $data Student and user data
     * @throws \App\Exceptions\BusinessLogicException If validation fails
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return Student
     */
    public function registerStudent(array $data): Student
    {
        try {
            // Validate required data
            $this->validateStudentData($data);

            // Start transaction
            return DB::transaction(function () use ($data) {
                // 1. Create user account first
                $userData = [
                    'name' => $data['name'],
                    'username' => $data['username'],
                    'email' => $data['email'],
                    'password' => $data['password'],
                    'phone_number' => $data['phone_number'] ?? null,
                    'role' => $data['role'] ?? RoleEnum::STUDENT->value,
                ];

                $user = $this->userRepository->create($userData);

                // 2. Create student profile
                $studentData = [
                    'birth_place' => $data['birth_place'],
                    'birth_date' => $data['birth_date'],
                    'gender' => $data['gender'],
                    'phone_number' => $data['phone_number'] ?? null,
                    'user_id' => $user->id,
                    'nis' => $data['nis'] ?? null,
                    'nisn' => $data['nisn'] ?? null,
                    'religion' => $data['religion'] ?? null,
                    'address' => $data['address'] ?? null,
                    'parent_name' => $data['parent_name'] ?? null,
                    'parent_phone' => $data['parent_phone'] ?? null,
                    'parent_occupation' => $data['parent_occupation'] ?? null,
                    'parent_address' => $data['parent_address'] ?? null,
                    'entry_year' => $data['entry_year'] ?? null,
                    'profile_picture' => $data['profile_picture'] ?? null,
                ];

                return $this->studentRepository->create($studentData);
            });
        } catch (BusinessLogicException | DatabaseException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal membuat data siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Update an existing student and user data.
     *
     * @param int $id Student ID
     * @param array $data Student and user data
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @throws \App\Exceptions\BusinessLogicException If validation fails
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function updateStudent(int $id, array $data): bool
    {
        try {
            // Find the student first to ensure it exists
            $student = $this->studentRepository->findById($id);
            $userId = $student->user->id;

            // Start transaction
            return DB::transaction(function () use ($id, $userId, $data) {
                $result = false;

                // 1. Update user data if provided
                if (
                    isset($data['name']) || isset($data['email']) || isset($data['username']) ||
                    isset($data['phone_number']) || isset($data['password']) || isset($data['role'])
                ) {
                    $userData = array_filter([
                        'name' => $data['name'] ?? null,
                        'email' => $data['email'] ?? null,
                        'username' => $data['username'] ?? null,
                        'phone_number' => $data['phone_number'] ?? null,
                        'password' => $data['password'] ?? null,
                        'role' => $data['role'] ?? null,
                    ], fn($value) => !is_null($value));

                    if (!empty($userData)) {
                        $result = $this->userRepository->update($userId, $userData) || $result;
                    }
                }

                // 2. Update student profile if provided
                $studentData = array_filter([
                    'birth_place' => $data['birth_place'] ?? null,
                    'birth_date' => $data['birth_date'] ?? null,
                    'gender' => $data['gender'] ?? null,
                    'phone_number' => $data['phone_number'] ?? null,
                    'nis' => $data['nis'] ?? null,
                    'nisn' => $data['nisn'] ?? null,
                    'religion' => $data['religion'] ?? null,
                    'address' => $data['address'] ?? null,
                    'parent_name' => $data['parent_name'] ?? null,
                    'parent_phone' => $data['parent_phone'] ?? null,
                    'parent_occupation' => $data['parent_occupation'] ?? null,
                    'parent_address' => $data['parent_address'] ?? null,
                    'entry_year' => $data['entry_year'] ?? null,
                    'profile_picture' => $data['profile_picture'] ?? null,
                ], fn($value) => !is_null($value));

                if (!empty($studentData)) {
                    $result = $this->studentRepository->update($id, $studentData) || $result;
                }

                return $result;
            });
        } catch (BusinessLogicException | DatabaseException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal memperbarui data siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Delete a student and associated user account.
     *
     * @param int $id Student ID
     * @throws \App\Exceptions\NotFoundException If the student is not found
     * @throws \App\Exceptions\BusinessLogicException If the student cannot be deleted
     * @throws \App\Exceptions\DatabaseException If there is a database error
     * @return bool
     */
    public function deleteStudent(int $id): bool
    {
        try {
            // Find the student first to ensure it exists
            $student = $this->studentRepository->findById($id);

            // Check if student has enrollments
            if ($student->classrooms()->exists()) {
                throw new BusinessLogicException('Siswa tidak dapat dihapus karena masih terdaftar di kelas');
            }

            // Start transaction
            return DB::transaction(function () use ($student) {
                // Delete the student
                $studentDeleted = $student->delete();

                // Delete the user
                $userDeleted = false;
                if ($student->user) {
                    $userDeleted = $student->user->delete();
                }

                return $studentDeleted && $userDeleted;
            });
        } catch (BusinessLogicException | NotFoundException $e) {
            // Just rethrow these exceptions as they are already properly formatted
            throw $e;
        } catch (Throwable $e) {
            // Convert any other exceptions to DatabaseException
            throw new DatabaseException('Gagal menghapus data siswa: ' . $e->getMessage(), null, [], [], 0, $e);
        }
    }

    /**
     * Validate student data.
     *
     * @param array $data
     * @throws \App\Exceptions\BusinessLogicException
     */
    private function validateStudentData(array $data): void
    {
        $requiredFields = [
            'name' => 'Nama',
            'username' => 'Username',
            'email' => 'Email',
            'password' => 'Password',
            'birth_place' => 'Tempat Lahir',
            'birth_date' => 'Tanggal Lahir',
            'gender' => 'Jenis Kelamin',
        ];

        $missingFields = [];

        foreach ($requiredFields as $field => $label) {
            if (empty($data[$field])) {
                $missingFields[] = $label;
            }
        }

        if (!empty($missingFields)) {
            throw new BusinessLogicException('Data berikut harus diisi: ' . implode(', ', $missingFields));
        }
    }
}

