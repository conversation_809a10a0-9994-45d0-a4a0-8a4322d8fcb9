<?php

namespace App\Repositories\Contracts;

use App\Models\User; // Staff adalah User
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\BusinessLogicException;

interface StaffRepositoryInterface
{
    /**
     * Get roles that are considered staff.
     */
    public function getAllowedRoles(): array;

    /**
     * Get all staff members with optional filtering.
     */
    public function getAll(array $filters = []): Collection;

    /**
     * Get all active staff members.
     */
    public function getAllActive(): Collection;

    /**
     * Find a staff member (User) by ID.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Jika user tidak ditemukan.
     * @throws \App\Exceptions\BusinessLogicException Jika user ditemukan tetapi bukan staf.
     */
    public function findById(int $id): User;

    /**
     * Create a new staff member (User).
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function create(array $data): User;

    /**
     * Update an existing staff member (User).
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function update(int $id, array $data): bool;

    /**
     * Delete a staff member (User).
     * @throws \Throwable Jika terjadi kesalahan pada level database.
     */
    public function delete(int $id): bool;

    /**
     * Count total number of staff members.
     */
    public function count(): int;

    /**
     * Count staff members by role.
     */
    public function countByRole(string $role): int;

    /**
     * Count staff members by role and status.
     */
    public function countByRoleAndStatus(string $role, string $status): int;
}
