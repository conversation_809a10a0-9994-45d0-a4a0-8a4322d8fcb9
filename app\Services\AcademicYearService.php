<?php

namespace App\Services;

use App\Repositories\Contracts\AcademicYearRepositoryInterface;
use App\Models\AcademicYear;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AcademicYearService
{
    protected $academicYearRepository;

    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(AcademicYearRepositoryInterface $academicYearRepository)
    {
        $this->academicYearRepository = $academicYearRepository;
    }

    /**
     * Get all academic years with optional filtering.
     *
     * @param  array  $filters  Optional filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllAcademicYears(array $filters = [])
    {
        return $this->academicYearRepository->getAll($filters);
    }

    /**
     * Get all academic years with pagination.
     *
     * @return \Illuminate\Pagination\LengthAwarePaginator
     */
    public function getAllAcademicYearsPaginated(int $perPage = 10)
    {
        return $this->academicYearRepository->getAllPaginated($perPage);
    }

    /**
     * Get an academic year by ID.
     *
     * @return AcademicYear
     */
    public function getAcademicYearById(int $id)
    {
        return $this->academicYearRepository->findById($id);
    }

    /**
     * Create a new academic year.
     *
     * @return AcademicYear
     */
    public function createAcademicYear(array $data)
    {
        return DB::transaction(fn() => $this->academicYearRepository->create($data));
    }

    /**
     * Update an existing academic year.
     *
     * @return AcademicYear
     */
    public function updateAcademicYear(int $id, array $data)
    {
        return DB::transaction(fn() => $this->academicYearRepository->update($id, $data));
    }

    /**
     * Change the status of an academic year.
     *
     * @return AcademicYear
     */
    public function changeAcademicYearStatus(int $id, string $status)
    {
        return DB::transaction(fn() => $this->academicYearRepository->changeStatus($id, $status));
    }

    /**
     * Get current active academic year.
     *
     * @return AcademicYear|null
     */
    public function getCurrentAcademicYear()
    {
        return $this->academicYearRepository->getCurrentAcademicYear();
    }

    /**
     * Get all active academic years.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveAcademicYears()
    {
        return $this->academicYearRepository->getActiveYears();
    }

    /**
     * Get all active academic years (alias for consistency with other services).
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllActiveAcademicYears()
    {
        return $this->getActiveAcademicYears();
    }

    /**
     * Get active academic year.
     *
     * @return AcademicYear|null The active academic year or null if none is active
     */
    public function getActiveAcademicYear(): ?AcademicYear
    {
        return $this->academicYearRepository->getActive();
    }

    /**
     * Check if academic year has related data.
     */
    public function hasRelatedData(int $id): bool
    {
        return $this->academicYearRepository->hasRelatedData($id);
    }

    /**
     * Get a list of all academic years for API.
     *
     * @return Collection
     */
    public function getAcademicYearsList()
    {
        return $this->academicYearRepository->getAcademicYearsList();
    }

    /**
     * Get academic year with its relations.
     *
     * @return AcademicYear
     */
    public function getAcademicYearWithRelations(int $id)
    {
        return $this->academicYearRepository->getAcademicYearWithRelations($id);
    }

    /**
     * Delete an academic year.
     */
    public function deleteAcademicYear(AcademicYear $academicYear): bool
    {
        return DB::transaction(fn() => $this->academicYearRepository->delete($academicYear));
    }
}

