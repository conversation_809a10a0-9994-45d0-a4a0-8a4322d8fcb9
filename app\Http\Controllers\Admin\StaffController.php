<?php

namespace App\Http\Controllers\Admin;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\StaffRequests\StaffFilterRequest;
use App\Http\Requests\StaffRequests\StaffStoreRequest;
use App\Http\Requests\StaffRequests\StaffUpdateRequest;
use App\Services\StaffService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response; // Tetap digunakan untuk abort() jika perlu
use App\Exceptions\BusinessLogicException;
use Throwable;

class StaffController extends Controller
{
    /**
     * StaffController constructor
     */
    public function __construct(protected StaffService $staffService)
    {
    }

    /**
     * Display a listing of staff members.
     * Metode ini mengasumsikan Anda akan memindahkan logika DataTables ke class terpisah (misalnya Yajra DataTables Class)
     * atau ke trait formatter jika Anda tidak menggunakan Yajra.
     */
    public function index(StaffFilterRequest $request): View|JsonResponse
    {
        // Jika Anda menggunakan Yajra DataTables Class (direkomendasikan):
        // use App\DataTables\StaffDataTable; // Impor kelas DataTables yang sesuai
        // public function index(StaffFilterRequest $request, StaffDataTable $dataTable): View|JsonResponse
        // if ($request->ajax()) {
        //     return $dataTable->ajax();
        // }

        // Jika Anda masih ingin memuat data di controller (misal untuk non-ajax view)
        // dan memformatnya secara manual (misal dengan trait terpisah):
        $staff = $this->staffService->getAll($request->validated());

        if ($request->ajax()) {
            return $this->formatStaffForDatatable($staff); // Panggil metode dari trait/helper
        }

        return view('admin.pages.staff.index', [
            'statuses' => UserStatus::dropdown(),
            'staff' => $staff, // Hanya jika tidak menggunakan DataTables class untuk view
            'roles' => [
                RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
            ],
        ]);
    }

    /**
     * Placeholder untuk format respons DataTables.
     * Pindahkan metode ini ke Trait terpisah atau Yajra DataTables Class.
     */
    private function formatStaffForDatatable($data): JsonResponse
    {
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('name', fn($row) => $row->name ?? '-')
            ->editColumn('email', fn($row) => $row->email ?? '-')
            ->editColumn('role', function ($row) {
                $role = $row->getRoleNames()->first();
                return RoleEnum::tryFrom($role)?->label() ?? '-';
            })
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->status->color() . ' text-uppercase">' . $row->status->label() . '</span>';
            })
            ->editColumn('created_at', function ($row) {
                return $row->created_at ? $row->created_at->format('d M Y') : '-';
            })
            ->addColumn('action', function ($row) {
                return view('admin.components.button-actions-v2', [
                    'id' => $row->id,
                    'edit' => route('admin.staff.edit', $row->id),
                    'destroy' => route('admin.staff.destroy', $row->id),
                ])->render();
            })
            ->rawColumns(['status', 'action'])
            ->make(true);
    }

    /**
     * Show the form for creating a new staff member.
     */
    public function create(): View
    {
        return view('admin.pages.staff.create', [
            'roles' => [
                RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
            ],
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created staff member.
     */
    public function store(StaffStoreRequest $request): JsonResponse
    {
        // Tidak ada try-catch di sini, biarkan pengecualian naik
        $staff = $this->staffService->create($request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil dibuat.',
            'data' => $staff,
        ], Response::HTTP_CREATED);
    }

    /**
     * Show the form for editing the specified staff member.
     */
    public function edit(int $id): View
    {
        // Jika findById melempar BusinessLogicException (misal: "Staf tidak ditemukan"),
        // Exception Handler Laravel akan menangkapnya dan bisa mengubahnya menjadi 404.
        $staff = $this->staffService->findById($id);

        return view('admin.pages.staff.edit', [
            'staff' => $staff,
            'roles' => [
                RoleEnum::ADMIN->value => RoleEnum::ADMIN->label(),
                RoleEnum::PRINCIPAL->value => RoleEnum::PRINCIPAL->label(),
                RoleEnum::TREASURER->value => RoleEnum::TREASURER->label(),
            ],
            'statuses' => UserStatus::dropdown(),
            'currentRole' => $staff->getRoleNames()->first(),
        ]);
    }

    /**
     * Update the specified staff member.
     */
    public function update(StaffUpdateRequest $request, int $id): JsonResponse
    {
        // Tidak ada try-catch di sini, biarkan pengecualian naik
        $this->staffService->update($id, $request->validated());

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil diperbarui.',
        ]);
    }

    /**
     * Remove the specified staff member.
     */
    public function destroy(int $id): JsonResponse
    {
        // Tidak ada try-catch di sini, biarkan pengecualian naik
        $this->staffService->delete($id);

        return response()->json([
            'success' => true,
            'message' => 'Data staf berhasil dihapus.',
        ]);
    }

    /**
     * Change staff member status.
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        // Validasi input dasar di controller
        $request->validate(['status' => 'required|boolean']);

        // Tidak ada try-catch di sini, biarkan pengecualian naik
        $updatedStaff = $this->staffService->changeStatus($id, $request->status);

        $statusLabel = $updatedStaff->status->label();

        return response()->json([
            'success' => true,
            'message' => "Status staf berhasil diubah menjadi {$statusLabel}.",
        ]);
    }
}
