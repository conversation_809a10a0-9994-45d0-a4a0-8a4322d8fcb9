<?php

namespace App\Services;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AuditLogService
{
    /**
     * Log user action for audit trail.
     */
    public static function logUserAction(string $action, string $resource, array $context = []): void
    {
        $user = Auth::user();
        
        Log::info('User Action', [
            'action' => $action,
            'resource' => $resource,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'user_email' => $user?->email,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log data modification for audit trail.
     */
    public static function logDataModification(string $operation, string $model, int $recordId, array $changes = [], array $context = []): void
    {
        $user = Auth::user();
        
        Log::info('Data Modification', [
            'operation' => $operation, // CREATE, UPDATE, DELETE
            'model' => $model,
            'record_id' => $recordId,
            'changes' => $changes,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log security event for monitoring.
     */
    public static function logSecurityEvent(string $event, string $severity = 'info', array $context = []): void
    {
        $user = Auth::user();
        
        Log::log($severity, 'Security Event', [
            'event' => $event,
            'severity' => $severity,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log business rule violation.
     */
    public static function logBusinessRuleViolation(string $rule, string $resource, array $context = []): void
    {
        $user = Auth::user();
        
        Log::warning('Business Rule Violation', [
            'rule' => $rule,
            'resource' => $resource,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log system performance metrics.
     */
    public static function logPerformanceMetric(string $operation, float $executionTime, array $context = []): void
    {
        Log::info('Performance Metric', [
            'operation' => $operation,
            'execution_time_ms' => round($executionTime * 1000, 2),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log bulk operation results.
     */
    public static function logBulkOperation(string $operation, int $totalRecords, int $successCount, int $failureCount, array $errors = []): void
    {
        $user = Auth::user();
        
        Log::info('Bulk Operation', [
            'operation' => $operation,
            'total_records' => $totalRecords,
            'success_count' => $successCount,
            'failure_count' => $failureCount,
            'success_rate' => $totalRecords > 0 ? round(($successCount / $totalRecords) * 100, 2) : 0,
            'errors' => $errors,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Log file operation (upload, download, delete).
     */
    public static function logFileOperation(string $operation, string $filename, string $path, int $fileSize = 0, array $context = []): void
    {
        $user = Auth::user();
        
        Log::info('File Operation', [
            'operation' => $operation,
            'filename' => $filename,
            'path' => $path,
            'file_size_bytes' => $fileSize,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'ip_address' => request()->ip(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log database query performance for monitoring.
     */
    public static function logSlowQuery(string $query, float $executionTime, array $bindings = []): void
    {
        if ($executionTime > 1.0) { // Log queries taking more than 1 second
            Log::warning('Slow Query Detected', [
                'query' => $query,
                'execution_time_ms' => round($executionTime * 1000, 2),
                'bindings' => $bindings,
                'timestamp' => now()->toISOString(),
            ]);
        }
    }

    /**
     * Log API request for monitoring.
     */
    public static function logApiRequest(string $method, string $endpoint, int $statusCode, float $responseTime, array $context = []): void
    {
        $user = Auth::user();
        
        Log::info('API Request', [
            'method' => $method,
            'endpoint' => $endpoint,
            'status_code' => $statusCode,
            'response_time_ms' => round($responseTime * 1000, 2),
            'user_id' => $user?->id,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log authentication events.
     */
    public static function logAuthEvent(string $event, ?int $userId = null, array $context = []): void
    {
        Log::info('Authentication Event', [
            'event' => $event, // LOGIN, LOGOUT, FAILED_LOGIN, PASSWORD_RESET, etc.
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log configuration changes.
     */
    public static function logConfigurationChange(string $setting, mixed $oldValue, mixed $newValue, array $context = []): void
    {
        $user = Auth::user();
        
        Log::info('Configuration Change', [
            'setting' => $setting,
            'old_value' => $oldValue,
            'new_value' => $newValue,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'timestamp' => now()->toISOString(),
            'context' => $context,
        ]);
    }

    /**
     * Log system health check results.
     */
    public static function logHealthCheck(string $component, bool $status, array $metrics = []): void
    {
        Log::info('Health Check', [
            'component' => $component,
            'status' => $status ? 'healthy' : 'unhealthy',
            'metrics' => $metrics,
            'timestamp' => now()->toISOString(),
        ]);
    }
}
