@extends('admin.layouts.app')

@section('title', 'Detail Guru')

@section('content')
    @include('admin.components.page-title', [
        'title' => 'Detail Guru',
        'breadcrumb' => 'Manajemen Akun',
    ])

    <div class="row">
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title mb-4">Informasi Guru</h5>
                    <div class="d-flex mb-2">
                        <div class="flex-shrink-0">
                            <span class="badge bg-primary-subtle text-primary fs-3 rounded-circle p-2">
                                <i class="ri-user-2-line"></i>
                            </span>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h5 class="mb-1">{{ $teacher->user->name }}</h5>
                            <p class="text-muted mb-0">
                                <span class="badge {{ $teacher->user->status->color() }}">
                                    {{ $teacher->user->status->label() }}
                                </span>
                            </p>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-lg-12">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-mail-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Email</h6>
                                    <p class="text-muted mb-0">{{ $teacher->user->email }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-user-3-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Username</h6>
                                    <p class="text-muted mb-0">{{ $teacher->user->username }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-shield-user-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Role</h6>
                                    <p class="text-muted mb-0">{{ $roles[$currentRole ?? ''] ?? 'Tidak ada role' }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-phone-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Nomor Telepon</h6>
                                    <p class="text-muted mb-0">{{ $teacher->user->phone_number ?: '-' }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-map-pin-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Tempat Lahir</h6>
                                    <p class="text-muted mb-0">{{ $teacher->birth_place }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-calendar-2-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Tanggal Lahir</h6>
                                    <p class="text-muted mb-0">{{ date('d F Y', strtotime($teacher->birth_date)) }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-12 mt-3">
                            <div class="list-group-item d-flex px-0">
                                <div class="me-2">
                                    <i class="ri-user-heart-line text-muted fs-16"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1">Jenis Kelamin</h6>
                                    <p class="text-muted mb-0">{{ $teacher->gender?->label() ?? '-' }}</p>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div class="hstack gap-2 justify-content-center mt-4">
                        <a href="{{ route('admin.teachers.edit', $teacher->id) }}" class="btn btn-primary btn-sm">
                            <i class="ri-edit-line align-bottom"></i> Edit Data
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">Informasi Akun</h5>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" data-bs-toggle="tab" href="#classes-tab" role="tab">
                                <i class="ri-building-line me-1"></i> Kelas yang Diampu
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" data-bs-toggle="tab" href="#account-tab" role="tab">
                                <i class="ri-user-settings-line me-1"></i> Pengaturan Akun
                            </a>
                        </li>
                    </ul>

                    <!-- Tab content -->
                    <div class="tab-content p-3">
                        <!-- Classes Tab -->
                        <div class="tab-pane active" id="classes-tab" role="tabpanel">
                            @if ($teacher->teacherAssignments->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-hover table-striped align-middle">
                                        <thead>
                                            <tr>
                                                <th>No.</th>
                                                <th>Nama Kelas</th>
                                                <th>Mata Pelajaran</th>
                                                <th>Tahun Akademik</th>
                                                <th>Wali Kelas</th>
                                                <th>Aksi</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($teacher->teacherAssignments as $key => $assignment)
                                                <tr>
                                                    <td>{{ $key + 1 }}</td>
                                                    <td>{{ $assignment->classroom->name }}</td>
                                                    <td>{{ $assignment->subject->name ?? '-' }}</td>
                                                    <td>{{ $assignment->academicYear->name }}</td>
                                                    <td>
                                                        @if ($assignment->is_homeroom_teacher)
                                                            <span class="badge bg-success">Ya</span>
                                                        @else
                                                            <span class="badge bg-secondary">Tidak</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <div class="hstack gap-2">
                                                            <a href="{{ route('admin.classrooms.show', $assignment->classroom->id) }}" class="link-primary fs-15" data-bs-toggle="tooltip" title="Lihat Detail Kelas">
                                                                <i class="ri-eye-line"></i>
                                                            </a>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="text-center py-4">
                                    <div class="avatar-md mx-auto mb-4">
                                        <div class="avatar-title bg-light text-secondary rounded-circle fs-24">
                                            <i class="ri-error-warning-line"></i>
                                        </div>
                                    </div>
                                    <h5>Belum ada penugasan!</h5>
                                    <p class="text-muted mb-4">Guru ini belum ditugaskan mengajar di kelas manapun</p>
                                </div>
                            @endif
                        </div>

                        <!-- Account Settings Tab -->
                        <div class="tab-pane" id="account-tab" role="tabpanel">
                            <div class="row">
                                <div class="col-lg-12">
                                    <div class="card border-0 shadow-none mb-0">
                                        <div class="card-header border-bottom-dashed align-items-center d-flex">
                                            <h4 class="card-title mb-0 flex-grow-1">Pengaturan Akun</h4>
                                            <div class="flex-shrink-0">
                                                <a href="{{ route('admin.teachers.edit', $teacher->id) }}" class="btn btn-soft-primary btn-sm">
                                                    <i class="ri-edit-line align-bottom"></i> Edit Akun
                                                </a>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table table-borderless mb-0">
                                                    <tbody>
                                                        <tr>
                                                            <th class="ps-0" scope="row" width="25%">Nama Lengkap</th>
                                                            <td class="text-muted">{{ $teacher->user->name }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Email</th>
                                                            <td class="text-muted">{{ $teacher->user->email }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Status</th>
                                                            <td>
                                                                <span class="badge {{ $teacher->user->status->color() }}">
                                                                    {{ $teacher->user->status->label() }}
                                                                </span>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Peran</th>
                                                            <td class="text-muted">{{ $roles[$currentRole ?? ''] ?? 'Tidak ada role' }}</td>
                                                        </tr>
                                                        <tr>
                                                            <th class="ps-0" scope="row">Terakhir Diperbarui</th>
                                                            <td class="text-muted">{{ $teacher->user->updated_at->format('d F Y H:i') }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="mt-4">
                                                <h5 class="mb-3">Tindakan Akun</h5>
                                                <div class="d-flex gap-2">
                                                    <button type="button" class="btn btn-soft-{{ $teacher->user->status->toBool() ? 'danger' : 'success' }} btn-sm"
                                                            onclick="changeStatus({{ $teacher->id }}, {{ $teacher->user->status->toBool() ? 'false' : 'true' }})">
                                                        <i class="ri-{{ $teacher->user->status->toBool() ? 'user-unfollow-line' : 'user-follow-line' }} align-bottom me-1"></i>
                                                        {{ $teacher->user->status->toBool() ? 'Nonaktifkan Akun' : 'Aktifkan Akun' }}
                                                    </button>
                                                    <button type="button" class="btn btn-soft-danger btn-sm" onclick="deleteTeacher({{ $teacher->id }})">
                                                        <i class="ri-delete-bin-line align-bottom me-1"></i> Hapus Akun
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@include('admin.partials.plugins.jquery')
@include('admin.partials.plugins.sweetalert')

@push('scripts')
    <script>
        // Function to delete teacher account
        async function deleteTeacher(id) {
            try {
                const url = "{{ route('admin.teachers.destroy', ':id') }}".replace(':id', id);

                // Confirm deletion
                const result = await Swal.fire({
                    title: 'Konfirmasi Hapus',
                    text: 'Anda yakin ingin menghapus guru ini?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Ya, Hapus!',
                    cancelButtonText: 'Batal',
                    reverseButtons: true
                });

                if (!result.isConfirmed) return;

                // Show loading
                Swal.fire({
                    title: 'Memproses...',
                    text: 'Sedang menghapus guru',
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // Execute deletion
                const response = await fetch(url, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                        'Accept': 'application/json'
                    }
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.message || 'Terjadi kesalahan saat menghapus data.');
                }

                // Show success alert
                await Swal.fire({
                    title: 'Berhasil!',
                    text: data.message,
                    icon: 'success',
                    timer: 2000,
                    showConfirmButton: false
                });

                // Redirect to index after successful deletion
                window.location.href = "{{ route('admin.teachers.index') }}";

            } catch (error) {
                await Swal.fire({
                    title: 'Hapus Gagal',
                    text: error.message,
                    icon: 'error'
                });
            }
        }

        // Function to change teacher account status
        function changeStatus(teacherId, status) {
            Swal.fire({
                title: status ? 'Aktifkan Akun' : 'Nonaktifkan Akun',
                text: status ? 'Apakah Anda yakin ingin mengaktifkan akun guru ini?' : 'Apakah Anda yakin ingin menonaktifkan akun guru ini?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Ya, ' + (status ? 'Aktifkan' : 'Nonaktifkan'),
                cancelButtonText: 'Batal',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'Memproses...',
                        text: 'Sedang mengubah status akun',
                        allowOutsideClick: false,
                        allowEscapeKey: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Send AJAX request to change status
                    $.ajax({
                        url: "{{ route('admin.teachers.change-status', ':id') }}".replace(':id', teacherId),
                        type: 'POST',
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: status
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Berhasil!',
                                    text: response.message,
                                    icon: 'success',
                                    showConfirmButton: false,
                                    timer: 1500
                                }).then(() => {
                                    // Reload page
                                    window.location.reload();
                                });
                            } else {
                                Swal.fire({
                                    title: 'Gagal',
                                    text: response.message || 'Terjadi kesalahan saat mengubah status akun',
                                    icon: 'error'
                                });
                            }
                        },
                        error: function(xhr) {
                            Swal.fire({
                                title: 'Gagal',
                                text: xhr.responseJSON?.message || 'Terjadi kesalahan saat mengubah status akun',
                                icon: 'error'
                            });
                        }
                    });
                }
            });
        }
    </script>
@endpush
