<?php

namespace App\Repositories\Contracts;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Collection as SupportCollection;

interface ProgramRepositoryInterface
{
    public function count(): int;

    public function getAll(): Collection;

    public function getAllActivePrograms(): Collection;

    public function findById(int $id);

    public function create(array $data);

    public function update(int $id, array $data);

    public function delete(int $id);

    public function getWithSubjects();

    public function getProgramClassrooms(int $programId);

    public function getProgramsWithClassroomStats(): SupportCollection;

    public function getProgramsList(): Collection;
}

