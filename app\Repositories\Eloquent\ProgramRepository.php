<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\ProgramRepositoryInterface;
use App\Models\Classroom;
use App\Models\Program;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\QueryException;
use Illuminate\Support\Collection as SupportCollection;

class ProgramRepository implements ProgramRepositoryInterface
{
    /**
     * Get all programs with optional filtering
     *
     * @param  array  $filters  Optional filters
     */
    public function getAll(array $filters = []): Collection
    {
        $query = Program::query()
            ->select(['id', 'name', 'code', 'description', 'status'])
            ->with(['classrooms.students', 'subjects'])
            ->withCount(['classrooms', 'subjects']);

        // Apply search filter if provided
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                    ->orWhere('code', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        return $query->orderBy('name')->get();
    }

    /**
     * Find a program by ID
     *
     * @return Program|null
     */
    public function findById(int $id)
    {
        return Program::findOrFail($id);
    }

    /**
     * Create a new program
     *
     * @return Program
     */
    public function create(array $data)
    {
        return Program::create($data);
    }

    /**
     * Update an existing program
     *
     * @return Program
     */
    public function update(int $id, array $data)
    {
        $program = $this->findById($id);
        $program->update($data);

        return $program;
    }

    /**
     * Delete a program
     *
     * @return bool
     */
    public function delete(int $id)
    {
        try {
            // delete the program
            return Program::destroy($id);
        } catch (QueryException $queryException) {
            // Log the exception or handle it as needed
            \Log::error($queryException->getMessage());
            throw $queryException;
        }
    }

    /**
     * Get programs with subjects
     *
     * @return Collection
     */
    public function getWithSubjects()
    {
        return Program::with('subjects')->get();
    }

    /**
     * Get classrooms for a specific program
     *
     * @return Collection
     */
    public function getProgramClassrooms(int $programId)
    {
        $program = $this->findById($programId);

        return $program->classrooms;
    }

    /**
     * Get total count of programs
     *
     * @return int The total number of programs
     */
    public function count(): int
    {
        return Program::count();
    }

    /**
     * Get all active programs
     */
    public function getAllActivePrograms(): Collection
    {
        return Program::query()
            ->select(['id', 'name', 'code', 'description', 'status'])
            ->where('status', 'active')
            ->with(['classrooms.students'])
            ->withCount(['classrooms'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get programs with classroom statistics
     */
    public function getProgramsWithClassroomStats(): SupportCollection
    {
        $programs = $this->getAll();

        return $programs->map(function (Program $program) {
            return (object) [
                'name' => $program->name,
                'code' => $program->code,
                'classrooms_count' => $program->classrooms_count,
                'active_classrooms' => $program->classrooms->where('status', 'active')->count(),
                'students_count' => $program->classrooms->sum(function ($classroom) {
                    return $classroom->students->count();
                }),
                'status' => $program->status,
            ];
        });
    }

    /**
     * Get a list of all programs for API
     */
    public function getProgramsList(): Collection
    {
        return Program::orderBy('name')->get(['id', 'name']);
    }
}

