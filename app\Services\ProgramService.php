<?php

namespace App\Services;

use App\Repositories\Contracts\ProgramRepositoryInterface;
use App\Models\Program;
use Illuminate\Support\Collection;

class ProgramService
{
    protected $programRepository;

    /**
     * Create a new service instance.
     *
     * @return void
     */
    public function __construct(ProgramRepositoryInterface $programRepository)
    {
        $this->programRepository = $programRepository;
    }

    /**
     * Get all programs with optional filtering.
     *
     * @param  array  $filters  Optional filters
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllPrograms(array $filters = [])
    {
        return $this->programRepository->getAll($filters);
    }

    /**
     * Get all active programs.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getAllActivePrograms()
    {
        return $this->programRepository->getAllActivePrograms();
    }

    /**
     * Get a program by ID.
     *
     * @return Program
     */
    public function getProgramById(int $id)
    {
        return $this->programRepository->findById($id);
    }

    /**
     * Create a new program.
     *
     * @return Program
     */
    public function createProgram(array $data)
    {
        return $this->programRepository->create($data);
    }

    /**
     * Update an existing program.
     *
     * @return Program
     */
    public function updateProgram(int $id, array $data)
    {
        return $this->programRepository->update($id, $data);
    }

    /**
     * Delete a program.
     *
     * @return bool
     */
    public function deleteProgram(int $id)
    {
        return $this->programRepository->delete($id);
    }

    /**
     * Get programs with subjects.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProgramsWithSubjects()
    {
        return $this->programRepository->getWithSubjects();
    }

    /**
     * Get classrooms for a program.
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getProgramClassrooms(int $programId)
    {
        return $this->programRepository->getProgramClassrooms($programId);
    }

    /**
     * Get total count of programs.
     *
     * @return int The total number of programs
     */
    public function getTotalPrograms(): int
    {
        return $this->programRepository->count();
    }

    /**
     * Get programs with classroom statistics.
     *
     * @return Collection Collection of program objects with classroom statistics
     */
    public function getProgramsWithClassroomStats(): Collection
    {
        return $this->programRepository->getProgramsWithClassroomStats();
    }

    /**
     * Get a list of all programs for API.
     */
    public function getProgramsList(): Collection
    {
        return $this->programRepository->getProgramsList();
    }
}

