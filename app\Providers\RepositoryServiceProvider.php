<?php

namespace App\Providers;

use App\Repositories\Contracts\ClassroomRepositoryInterface;
use App\Repositories\Contracts\ShiftRepositoryInterface;
use App\Repositories\Eloquent\ClassroomRepository;
use App\Repositories\Eloquent\ShiftRepository;
use Illuminate\Support\ServiceProvider;

final class RepositoryServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->bind(ClassroomRepositoryInterface::class, ClassroomRepository::class);
        $this->app->bind(ShiftRepositoryInterface::class, ShiftRepository::class);
    }
}
