<?php

namespace App\Repositories\Contracts;

use App\Models\Classroom;
use Illuminate\Database\Eloquent\Collection;

interface ClassroomRepositoryInterface
{
    /**
     * Get all classrooms.
     *
     * @return Collection
     */
    public function getAll();

    /**
     * Get active classrooms.
     *
     * @return Collection
     */
    public function getActiveClassrooms();

    /**
     * Get classrooms by academic year.
     *
     * @return Collection
     */
    public function getByAcademicYear(int $academicYearId);

    /**
     * Get active classrooms by academic year.
     *
     * @return Collection
     */
    public function getActiveByAcademicYear(int $academicYearId);

    /**
     * Find a classroom by ID.
     *
     * @return Classroom
     */
    public function findById(int $id);

    /**
     * Create a new classroom.
     *
     * @return Classroom
     */
    public function create(array $data);

    /**
     * Update a classroom.
     *
     * @return bool
     */
    public function update(int $id, array $data);

    /**
     * Change the teacher assigned to a classroom.
     *
     * @return bool
     */
    public function changeTeacher(int $classroomId, int $teacherId);

    /**
     * Get students in a classroom.
     *
     * @return Collection
     */
    public function getClassroomStudents(int $classroomId);

    /**
     * Get available students for a classroom (not enrolled in any class for current academic year).
     *
     * @return Collection
     */
    public function getAvailableStudents(int $academicYearId);

    /**
     * Get classroom schedule.
     *
     * @return Collection
     */
    public function getClassroomSchedule(int $classroomId);

    public function count(): int;

    public function getActiveCount(): int;

    /**
     * Remove a student from a classroom.
     */
    public function removeStudentFromClassroom(int $classroomId, int $studentId): bool;

    /**
     * Enroll multiple students to a classroom.
     */
    public function enrollStudentsToClassroom(int $classroomId, array $studentIds): bool;

    /**
     * Check if a student is already enrolled in any classroom for a specific academic year.
     */
    public function isStudentEnrolledInAcademicYear(int $studentId, int $academicYearId): bool;

    /**
     * Delete a classroom.
     */
    public function deleteClassroom(int $id): bool;
}

