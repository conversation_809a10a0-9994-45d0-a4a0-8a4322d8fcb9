<?php

namespace App\Services;

use App\Repositories\Contracts\AcademicYearRepositoryInterface;
use App\Repositories\Contracts\AttendanceRepositoryInterface;
use App\Repositories\Contracts\ClassroomRepositoryInterface;
use App\Repositories\Contracts\ClassScheduleRepositoryInterface;
use App\Repositories\Contracts\LeaveRequestRepositoryInterface;
use App\Repositories\Contracts\LessonHourRepositoryInterface;
use App\Repositories\Contracts\ProgramRepositoryInterface;
use App\Repositories\Contracts\ShiftRepositoryInterface;
use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\SubjectRepositoryInterface;
use App\Repositories\Contracts\TeacherAssignmentRepositoryInterface;
use App\Repositories\Contracts\TeacherRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class ServiceProviderValidationService
{
    /**
     * Repository interfaces that should be bound.
     */
    private const REPOSITORY_INTERFACES = [
        UserRepositoryInterface::class,
        TeacherRepositoryInterface::class,
        StudentRepositoryInterface::class,
        StaffRepositoryInterface::class,
        ProgramRepositoryInterface::class,
        AcademicYearRepositoryInterface::class,
        ClassroomRepositoryInterface::class,
        SubjectRepositoryInterface::class,
        LessonHourRepositoryInterface::class,
        TeacherAssignmentRepositoryInterface::class,
        ClassScheduleRepositoryInterface::class,
        ShiftRepositoryInterface::class,
        AttendanceRepositoryInterface::class,
        LeaveRequestRepositoryInterface::class,
    ];

    /**
     * Service classes that should be resolvable.
     */
    private const SERVICE_CLASSES = [
        UserService::class,
        StudentService::class,
        TeacherService::class,
        StaffService::class,
        ClassroomService::class,
        SubjectService::class,
        AcademicYearService::class,
        ErrorHandlingService::class,
        AuditLogService::class,
    ];

    /**
     * Validate all repository interface bindings.
     */
    public function validateRepositoryBindings(): array
    {
        $results = [];
        
        foreach (self::REPOSITORY_INTERFACES as $interface) {
            try {
                $instance = App::make($interface);
                $results[$interface] = [
                    'status' => 'success',
                    'implementation' => get_class($instance),
                    'message' => 'Successfully resolved'
                ];
            } catch (\Exception $e) {
                $results[$interface] = [
                    'status' => 'error',
                    'implementation' => null,
                    'message' => $e->getMessage()
                ];
                
                Log::error("Repository binding validation failed for {$interface}: " . $e->getMessage());
            }
        }
        
        return $results;
    }

    /**
     * Validate service class resolution.
     */
    public function validateServiceResolution(): array
    {
        $results = [];
        
        foreach (self::SERVICE_CLASSES as $serviceClass) {
            try {
                $instance = App::make($serviceClass);
                $results[$serviceClass] = [
                    'status' => 'success',
                    'instance' => get_class($instance),
                    'message' => 'Successfully resolved'
                ];
            } catch (\Exception $e) {
                $results[$serviceClass] = [
                    'status' => 'error',
                    'instance' => null,
                    'message' => $e->getMessage()
                ];
                
                Log::error("Service resolution validation failed for {$serviceClass}: " . $e->getMessage());
            }
        }
        
        return $results;
    }

    /**
     * Validate dependency injection in controllers.
     */
    public function validateControllerDependencies(): array
    {
        $controllers = [
            'App\Http\Controllers\Admin\StudentController',
            'App\Http\Controllers\Admin\TeacherController',
            'App\Http\Controllers\Admin\UserController',
            'App\Http\Controllers\Admin\ClassroomController',
            'App\Http\Controllers\Admin\SubjectController',
        ];
        
        $results = [];
        
        foreach ($controllers as $controllerClass) {
            try {
                if (class_exists($controllerClass)) {
                    $instance = App::make($controllerClass);
                    $results[$controllerClass] = [
                        'status' => 'success',
                        'instance' => get_class($instance),
                        'message' => 'Successfully resolved with dependencies'
                    ];
                } else {
                    $results[$controllerClass] = [
                        'status' => 'warning',
                        'instance' => null,
                        'message' => 'Controller class does not exist'
                    ];
                }
            } catch (\Exception $e) {
                $results[$controllerClass] = [
                    'status' => 'error',
                    'instance' => null,
                    'message' => $e->getMessage()
                ];
                
                Log::error("Controller dependency validation failed for {$controllerClass}: " . $e->getMessage());
            }
        }
        
        return $results;
    }

    /**
     * Run comprehensive validation of all bindings and dependencies.
     */
    public function runComprehensiveValidation(): array
    {
        $startTime = microtime(true);
        
        $results = [
            'timestamp' => now()->toISOString(),
            'repository_bindings' => $this->validateRepositoryBindings(),
            'service_resolution' => $this->validateServiceResolution(),
            'controller_dependencies' => $this->validateControllerDependencies(),
        ];
        
        // Calculate summary statistics
        $repositoryStats = $this->calculateStats($results['repository_bindings']);
        $serviceStats = $this->calculateStats($results['service_resolution']);
        $controllerStats = $this->calculateStats($results['controller_dependencies']);
        
        $results['summary'] = [
            'total_validations' => $repositoryStats['total'] + $serviceStats['total'] + $controllerStats['total'],
            'successful_validations' => $repositoryStats['success'] + $serviceStats['success'] + $controllerStats['success'],
            'failed_validations' => $repositoryStats['error'] + $serviceStats['error'] + $controllerStats['error'],
            'warning_validations' => $repositoryStats['warning'] + $serviceStats['warning'] + $controllerStats['warning'],
            'execution_time_ms' => round((microtime(true) - $startTime) * 1000, 2),
            'repository_bindings' => $repositoryStats,
            'service_resolution' => $serviceStats,
            'controller_dependencies' => $controllerStats,
        ];
        
        // Log validation results
        if ($results['summary']['failed_validations'] > 0) {
            Log::warning('Service provider validation completed with errors', $results['summary']);
        } else {
            Log::info('Service provider validation completed successfully', $results['summary']);
        }
        
        return $results;
    }

    /**
     * Calculate statistics for validation results.
     */
    private function calculateStats(array $results): array
    {
        $stats = [
            'total' => count($results),
            'success' => 0,
            'error' => 0,
            'warning' => 0,
        ];
        
        foreach ($results as $result) {
            $stats[$result['status']]++;
        }
        
        return $stats;
    }

    /**
     * Get detailed binding information for debugging.
     */
    public function getBindingDetails(): array
    {
        $bindings = [];
        
        foreach (self::REPOSITORY_INTERFACES as $interface) {
            $bindings[$interface] = [
                'interface' => $interface,
                'is_bound' => App::bound($interface),
                'is_singleton' => App::isShared($interface),
                'concrete' => App::bound($interface) ? get_class(App::make($interface)) : null,
            ];
        }
        
        return $bindings;
    }

    /**
     * Validate that repository implementations match their interfaces.
     */
    public function validateInterfaceImplementations(): array
    {
        $results = [];
        
        foreach (self::REPOSITORY_INTERFACES as $interface) {
            try {
                $instance = App::make($interface);
                $implements = class_implements($instance);
                
                $results[$interface] = [
                    'status' => in_array($interface, $implements) ? 'success' : 'error',
                    'implementation' => get_class($instance),
                    'implements_interface' => in_array($interface, $implements),
                    'all_interfaces' => array_values($implements),
                    'message' => in_array($interface, $implements) 
                        ? 'Implementation correctly implements interface'
                        : 'Implementation does not implement the expected interface'
                ];
            } catch (\Exception $e) {
                $results[$interface] = [
                    'status' => 'error',
                    'implementation' => null,
                    'implements_interface' => false,
                    'all_interfaces' => [],
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }

    /**
     * Test basic functionality of resolved repositories.
     */
    public function testRepositoryFunctionality(): array
    {
        $results = [];
        
        // Test basic methods that should exist on all repositories
        $basicMethods = ['getAll', 'findById', 'create', 'update', 'delete'];
        
        foreach (self::REPOSITORY_INTERFACES as $interface) {
            try {
                $instance = App::make($interface);
                $methods = get_class_methods($instance);
                
                $methodTests = [];
                foreach ($basicMethods as $method) {
                    $methodTests[$method] = in_array($method, $methods);
                }
                
                $results[$interface] = [
                    'status' => 'success',
                    'implementation' => get_class($instance),
                    'method_tests' => $methodTests,
                    'total_methods' => count($methods),
                    'message' => 'Repository functionality test completed'
                ];
            } catch (\Exception $e) {
                $results[$interface] = [
                    'status' => 'error',
                    'implementation' => null,
                    'method_tests' => [],
                    'total_methods' => 0,
                    'message' => $e->getMessage()
                ];
            }
        }
        
        return $results;
    }
}
