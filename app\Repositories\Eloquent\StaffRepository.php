<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use App\Exceptions\BusinessLogicException; // Untuk validasi user bukan staff
use Throwable;

class StaffRepository implements StaffRepositoryInterface
{
    protected UserRepositoryInterface $userRepository;

    public function __construct(UserRepositoryInterface $userRepository)
    {
        $this->userRepository = $userRepository;
    }

    /**
     * Defines roles considered as 'staff'.
     * Ini adalah logika konfigurasi, bukan bisnis kompleks, sehingga bisa di sini.
     */
    public function getAllowedRoles(): array
    {
        // Menggunakan RoleEnum::cases() untuk mendapatkan semua kasus enum
        return array_filter(
            RoleEnum::values(),
            fn($role) => !str_contains($role, 'teacher') && !str_contains($role, 'student')
        );
    }

    /**
     * Get all staff members with optional filtering.
     * Mendelegasikan ke UserRepository's getAll dan memfilter berdasarkan peran yang diizinkan.
     */
    public function getAll(array $filters = []): Collection
    {
        $staffRoles = $this->getAllowedRoles();
        $filters['role_in'] = array_merge($filters['role_in'] ?? [], $staffRoles);

        return $this->userRepository->getAll($filters);
    }

    /**
     * Get all active staff members.
     */
    public function getAllActive(): Collection
    {
        return $this->getAll(['status' => UserStatus::Active->value]);
    }

    /**
     * Find a staff member by ID.
     * Mendelegasikan ke UserRepository dan kemudian memvalidasi apakah user yang ditemukan adalah staf.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException Jika user tidak ditemukan.
     * @throws \App\Exceptions\BusinessLogicException Jika user ditemukan tetapi bukan staf.
     */
    public function findById(int $id): User
    {
        try {
            $user = $this->userRepository->findById($id);

            // Validasi bahwa user yang ditemukan memang staf
            if (!$user->hasAnyRole($this->getAllowedRoles())) {
                throw new BusinessLogicException("User dengan ID {$id} bukan merupakan staf.");
            }

            return $user;
        } catch (ModelNotFoundException $e) {
            throw $e; // Biarkan ModelNotFoundException naik ke Service
        } catch (Throwable $e) {
            throw new BusinessLogicException('Terjadi kesalahan saat mencari staf: ' . $e->getMessage(), 0, $e);
        }
    }

    /**
     * Create a new staff member (which is essentially a new User).
     * Mendelegasikan pembuatan ke UserRepository. Logika role assignment di UserService.
     */
    public function create(array $data): User
    {
        // Method ini dipanggil oleh StaffService, yang sudah memvalidasi peran
        // dan akan memanggil UserService->create()
        try {
            return $this->userRepository->create($data);
        } catch (Throwable $e) {
            throw $e; // Biarkan error naik ke Service
        }
    }

    /**
     * Update an existing staff member (User).
     * Mendelegasikan pembaruan ke UserRepository. Logika role assignment/update di UserService.
     */
    public function update(int $id, array $data): bool
    {
        try {
            return $this->userRepository->update($id, $data);
        } catch (Throwable $e) {
            throw $e; // Biarkan error naik ke Service
        }
    }

    /**
     * Delete a staff member (User).
     * Mendelegasikan penghapusan ke UserRepository.
     */
    public function delete(int $id): bool
    {
        try {
            return $this->userRepository->delete($id);
        } catch (Throwable $e) {
            throw $e; // Biarkan error naik ke Service
        }
    }

    /**
     * Count total number of staff members.
     * Memfilter berdasarkan peran yang diizinkan.
     */
    public function count(): int
    {
        return $this->getAll(['status' => null])->count();
    }

    /**
     * Count staff members by role.
     * Mendelegasikan ke UserRepository.
     */
    public function countByRole(string $role): int
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            return 0;
        }
        return $this->userRepository->countUsersByRole($role);
    }

    /**
     * Count staff members by role and status.
     * Metode baru yang diperlukan oleh StaffService untuk validasi.
     */
    public function countByRoleAndStatus(string $role, string $status): int
    {
        if (!in_array($role, $this->getAllowedRoles())) {
            return 0;
        }
        return $this->userRepository->getAll(['role' => $role, 'status' => $status])->count();
    }
}
