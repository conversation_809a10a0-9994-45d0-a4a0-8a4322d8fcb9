<?php

namespace App\Repositories\Contracts;

interface ScheduleRepositoryInterface
{
    public function createSchedule(array $data);

    public function updateSchedule(int $id, array $data);

    public function deleteSchedule(int $id);

    public function getClassroomDailySchedule(int $classroomId, string $date);

    public function getTeacherWeeklySchedule(int $teacherId, string $weekStart);

    public function checkScheduleConflict(array $data);
}

