<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\ClassScheduleRepositoryInterface;
use App\Exceptions\ClassScheduleException;
use App\Models\ClassSchedule;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ClassScheduleRepository implements ClassScheduleRepositoryInterface
{
    /**
     * ClassSchedule model instance
     */
    protected ClassSchedule $classScheduleModel;

    /**
     * ClassScheduleRepository constructor
     */
    public function __construct(ClassSchedule $classScheduleModel)
    {
        $this->classScheduleModel = $classScheduleModel;
    }

    /**
     * Get all class schedules
     */
    public function getAllClassSchedules(): Collection
    {
        return $this->classScheduleModel->with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])->get();
    }

    /**
     * Get a class schedule by ID
     *
     * @param  mixed  $id
     */
    public function getClassScheduleById($id): ClassSchedule
    {
        try {
            return $this->classScheduleModel->with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])->findOrFail($id);
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            throw ClassScheduleException::notFound('Jadwal kelas tidak ditemukan');
        }
    }

    /**
     * Create a new class schedule
     */
    public function createClassSchedule(array $data): ClassSchedule
    {
        try {
            return $this->classScheduleModel->create([
                'teacher_assignment_id' => $data['teacher_assignment_id'],
                'lesson_hour_id' => $data['lesson_hour_id'],
                'day_of_week' => $data['day_of_week'],
                'status' => $data['status'] ?? 'active',
            ]);
        } catch (\Exception $e) {
            Log::error('Create class schedule failed: '.$e->getMessage());
            throw new \Exception('Gagal menyimpan jadwal kelas: '.$e->getMessage());
        }
    }

    /**
     * Update a class schedule
     *
     * @param  mixed  $id
     */
    public function updateClassSchedule($id, array $data): bool
    {
        try {
            $classSchedule = $this->getClassScheduleById($id);

            $updateData = [
                'teacher_assignment_id' => $data['teacher_assignment_id'] ?? $classSchedule->teacher_assignment_id,
                'lesson_hour_id' => $data['lesson_hour_id'] ?? $classSchedule->lesson_hour_id,
                'day_of_week' => $data['day_of_week'] ?? $classSchedule->day_of_week,
                'status' => $data['status'] ?? $classSchedule->status,
            ];

            // Check if data has actually changed
            if (
                $classSchedule->teacher_assignment_id === $updateData['teacher_assignment_id'] &&
                $classSchedule->lesson_hour_id === $updateData['lesson_hour_id'] &&
                $classSchedule->day_of_week === $updateData['day_of_week'] &&
                $classSchedule->status === $updateData['status']
            ) {
                return false;
            }

            $classSchedule->fill($updateData);
            $classSchedule->save();

            return true;
        } catch (\Exception $e) {
            Log::error('Update class schedule failed: '.$e->getMessage());
            throw new \Exception('Gagal memperbarui jadwal kelas: '.$e->getMessage());
        }
    }

    /**
     * Delete a class schedule
     *
     * @param  mixed  $id
     */
    public function deleteClassSchedule($id): bool
    {
        try {
            $classSchedule = $this->getClassScheduleById($id);
            $classSchedule->delete();

            return true;
        } catch (\Exception $e) {
            Log::error('Delete class schedule failed: '.$e->getMessage());
            throw new \Exception('Gagal menghapus jadwal kelas: '.$e->getMessage());
        }
    }

    /**
     * Get schedules by classroom and academic year
     */
    public function getSchedulesByClassroomAndAcademicYear(int $classroomId, int $academicYearId): Collection
    {
        return $this->classScheduleModel
            ->with(['teacherAssignment.teacher.user', 'teacherAssignment.subject', 'lessonHour'])
            ->whereHas('teacherAssignment', function ($query) use ($classroomId, $academicYearId) {
                $query->where('classroom_id', $classroomId)
                    ->where('academic_year_id', $academicYearId);
            })
            ->where('status', 'active')
            ->get();
    }

    /**
     * Find existing schedule
     */
    public function findExistingSchedule(int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): ?ClassSchedule
    {
        $query = $this->classScheduleModel
            ->where('lesson_hour_id', $lessonHourId)
            ->where('day_of_week', $dayOfWeek)
            ->where('status', 'active');

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->first();
    }

    /**
     * Check for teacher schedule conflict
     */
    public function checkTeacherScheduleConflict(int $teacherId, int $lessonHourId, string $dayOfWeek, ?int $excludeId = null): bool
    {
        $query = $this->classScheduleModel
            ->whereHas('teacherAssignment', function ($query) use ($teacherId) {
                $query->where('teacher_id', $teacherId);
            })
            ->where('lesson_hour_id', $lessonHourId)
            ->where('day_of_week', $dayOfWeek)
            ->where('status', 'active');

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}

