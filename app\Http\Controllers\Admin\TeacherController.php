<?php

namespace App\Http\Controllers\Admin;

use App\Enums\GenderEnum;
use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Http\Requests\TeacherRequests\TeacherFilterRequest;
use App\Http\Requests\TeacherRequests\TeacherStoreRequest;
use App\Http\Requests\TeacherRequests\TeacherUpdateRequest;
use App\Services\TeacherService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class TeacherController extends Controller
{
    /**
     * TeacherController constructor
     */
    public function __construct(protected TeacherService $teacherService)
    {
    }

    /**
     * Display a listing of teachers
     * (Asumsi Anda akan memindahkan DataTable logic ke class terpisah seperti UserDataTable)
     */
    public function index(TeacherFilterRequest $request): View|JsonResponse
    {
        // Jika Anda menggunakan Yajra DataTables Class, panggil di sini:
        // use App\DataTables\TeacherDataTable;
        // public function index(TeacherFilterRequest $request, TeacherDataTable $dataTable): View|JsonResponse
        // if ($request->ajax()) {
        //     return $dataTable->ajax();
        // }

        // Jika Anda masih ingin memuat data di controller (misal untuk non-ajax view)
        $teachers = $this->teacherService->getAll($request->validated());

        if ($request->ajax()) {
            // Jika tidak pakai Yajra Class, Anda bisa buat trait formatter terpisah
            // Atau mengembalikan JSON polos dan frontend yang memformat
            return $this->formatTeachersForDatatable($teachers); // Metode ini akan kita buat di trait
        }

        return view('admin.pages.teacher.index', [
            'statuses' => UserStatus::dropdown(),
            'teachers' => $teachers, // Hanya jika tidak menggunakan DataTables class untuk view
            'roles' => [
                RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
            ],
            'genders' => GenderEnum::options(),
        ]);
    }

    /**
     * Format response for DataTables.
     * Metode ini akan dipindahkan ke Trait atau Yajra DataTable Class.
     * Untuk tujuan demonstrasi refactoring controller ini, saya sertakan placeholder.
     */
    private function formatTeachersForDatatable($data): JsonResponse
    {
        // Pindahkan logic dari method datatableResponse lama ke sini
        // Atau lebih baik, gunakan Yajra DataTables Class seperti saran sebelumnya
        return datatables()
            ->of($data)
            ->addIndexColumn()
            ->editColumn('user.name', fn($row) => $row->user->name ?? '-')
            ->editColumn('user.email', fn($row) => $row->user->email ?? '-')
            ->editColumn('user.role', fn($row) => $row->user->roleLabel() ?? '-')
            ->editColumn('gender', function ($row) {
                // Pastikan GenderEnum memiliki method label()
                return $row->gender?->label() ?? '-';
            })
            ->editColumn('birth_date', fn($row) => $row->birth_date ? date('d/m/Y', strtotime($row->birth_date)) : '-')
            ->editColumn('status', function ($row) {
                return '<span class="badge bg-' . $row->user->status->color() . ' text-uppercase">' . $row->user->status->label() . '</span>';
            })
            ->addColumn(
                'action',
                fn($row) => view('admin.components.button-actions-v2', [
                    'id' => $row->id,
                    'edit' => route('admin.teachers.edit', $row->id),
                    'destroy' => route('admin.teachers.destroy', $row->id),
                ])->render() // Penting: render view Blade
            )
            ->rawColumns(['status', 'action'])
            ->make(true);
    }


    /**
     * Show the form for creating a new teacher.
     */
    public function create(): View
    {
        return view('admin.pages.teacher.create', [
            'roles' => [
                RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
            ],
            'statuses' => UserStatus::dropdown(),
        ]);
    }

    /**
     * Store a newly created teacher.
     */
    public function store(TeacherStoreRequest $request): JsonResponse
    {
        try {
            $teacher = $this->teacherService->create($request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dibuat.',
                'data' => $teacher,
            ], 201); // 201 Created
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat membuat data guru.',
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified teacher.
     */
    public function edit(int $id): View
    {
        try {
            $teacher = $this->teacherService->findById($id); // Service akan melempar BusinessLogicException jika tidak ditemukan

            return view('admin.pages.teacher.edit', [
                'teacher' => $teacher,
                'roles' => [
                    RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                    RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
                ],
                'statuses' => UserStatus::dropdown(),
                'currentRole' => $teacher->user->getRoleNames()->first(),
            ]);
        } catch (BusinessLogicException $e) {
            // Karena ini untuk tampilan, kita abort 404 jika guru tidak ditemukan.
            // Exception Handler Laravel akan mengubah ini menjadi halaman 404.
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Update the specified teacher.
     */
    public function update(TeacherUpdateRequest $request, int $id): JsonResponse
    {
        try {
            $this->teacherService->update($id, $request->validated());

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil diperbarui.',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat memperbarui data guru.',
            ], 500);
        }
    }

    /**
     * Remove the specified teacher.
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $this->teacherService->deleteTeacher($id);

            return response()->json([
                'success' => true,
                'message' => 'Data guru berhasil dihapus.',
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat menghapus data guru.',
            ], 500);
        }
    }

    /**
     * Display the specified teacher.
     */
    public function show(int $id): View
    {
        try {
            $teacher = $this->teacherService->findById($id);

            return view('admin.pages.teacher.show', [
                'teacher' => $teacher,
                'roles' => [
                    RoleEnum::SUBJECT_TEACHER->value => RoleEnum::SUBJECT_TEACHER->label(),
                    RoleEnum::SUBSTITUTE_TEACHER->value => RoleEnum::SUBSTITUTE_TEACHER->label(),
                ],
                'currentRole' => $teacher->user->getRoleNames()->first(),
            ]);
        } catch (BusinessLogicException $e) {
            abort(Response::HTTP_NOT_FOUND, $e->getMessage());
        }
    }

    /**
     * Change the status of a teacher's account.
     */
    public function changeStatus(Request $request, int $id): JsonResponse
    {
        // Validasi input langsung di controller untuk perubahan status sederhana
        $request->validate(['status' => 'required|boolean']);

        try {
            $teacher = $this->teacherService->changeTeacherStatus($id, $request->status);
            $statusLabel = $teacher->user->status->label();

            return response()->json([
                'success' => true,
                'message' => "Status guru berhasil diubah menjadi {$statusLabel}.",
            ]);
        } catch (BusinessLogicException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 400);
        } catch (Throwable $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan saat mengubah status guru.',
            ], 500);
        }
    }
}
