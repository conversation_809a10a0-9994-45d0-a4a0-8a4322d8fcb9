<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\SubjectRepositoryInterface;
use App\Models\Subject;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SubjectRepository implements SubjectRepositoryInterface
{
    /**
     * Subject model instance
     */
    private Subject $subjectModel;

    /**
     * SubjectRepository constructor.
     */
    public function __construct(Subject $subjectModel)
    {
        $this->subjectModel = $subjectModel;
    }

    /**
     * Get all subjects with optional filtering
     */
    public function getAllSubjects(array $filters): Collection
    {
        $query = $this->subjectModel->with('program');
        $query = $this->applyFilters($query, $filters);

        return $query->get();
    }

    /**
     * Get a subject by ID
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException
     */
    public function getSubjectById(int $id): Subject
    {
        return $this->subjectModel->with('program')->findOrFail($id);
    }

    /**
     * Create a new subject
     *
     * @throws \Exception
     */
    public function createSubject(array $data): Subject
    {
        DB::beginTransaction();
        try {
            $subject = $this->subjectModel->create([
                'name' => $data['name'],
                'program_id' => $data['program_id'],
            ]);

            DB::commit();

            return $subject;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Create subject failed: '.$e->getMessage());
            throw new \Exception('Gagal menyimpan mata pelajaran: '.$e->getMessage());
        }
    }

    /**
     * Update an existing subject
     *
     * @throws \Exception
     */
    public function updateSubject(int $id, array $data): bool
    {
        DB::beginTransaction();
        try {
            $subject = $this->getSubjectById($id);

            $updateData = [
                'name' => $data['name'] ?? $subject->name,
                'program_id' => $data['program_id'] ?? $subject->program_id,
            ];

            // Check if data has actually changed
            if ($subject->name === $updateData['name'] && $subject->program_id === $updateData['program_id']) {
                DB::rollBack();

                return false;
            }

            $subject->fill($updateData);
            $subject->save();

            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Update subject failed: '.$e->getMessage());
            throw new \Exception('Gagal memperbarui mata pelajaran: '.$e->getMessage());
        }
    }

    /**
     * Delete a subject
     *
     * @throws \Exception
     */
    public function deleteSubject(int $id): bool
    {
        DB::beginTransaction();
        try {
            $subject = $this->getSubjectById($id);

            // Check if the subject has any teacher assignments
            if ($subject->teacherAssignments()->count() > 0) {
                throw new \Exception('Mata pelajaran ini tidak dapat dihapus karena masih digunakan pada data guru');
            }

            $subject->delete();
            DB::commit();

            return true;

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Delete subject failed: '.$e->getMessage());
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * Apply filters to the query
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function applyFilters($query, array $filters)
    {
        // Filter by program
        if (! empty($filters['program_id'])) {
            $query->where('program_id', $filters['program_id']);
        }

        // Search by name
        if (! empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    public function count(): int
    {
        return Subject::count();
    }
}

