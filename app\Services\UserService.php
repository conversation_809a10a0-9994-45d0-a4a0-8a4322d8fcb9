<?php

namespace App\Services;

use App\Enums\RoleEnum;
use App\Enums\UserStatus;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Exceptions\BusinessLogicException;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Throwable;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class UserService
{
    protected const MAX_ADMIN_COUNT = 5;

    public function __construct(
        protected UserRepositoryInterface $userRepository
    ) {
    }

    /**
     * Get all users with filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        return $this->userRepository->getAll($filters);
    }

    /**
     * Get all active users.
     */
    public function getAllActiveUsers(): Collection
    {
        return $this->userRepository->getAll(['status' => UserStatus::Active->value]);
    }

    /**
     * Get a user by ID.
     * Converts ModelNotFoundException to BusinessLogicException for consistency.
     */
    public function findById(int $id): User
    {
        try {
            return $this->userRepository->findById($id);
        } catch (ModelNotFoundException $e) {
            // Mengubah ModelNotFoundException menjadi BusinessLogicException
            throw new BusinessLogicException("Pengguna tidak ditemukan.");
        }
    }

    /**
     * Create a new user with validation and business rules.
     */
    public function create(array $data): User
    {
        return DB::transaction(function () use ($data) {
            try {
                if (isset($data['role']) && $data['role'] === RoleEnum::ADMIN->value) {
                    $this->validateAdminLimitForCreation();
                }

                $user = $this->userRepository->create($data);

                // Logika penetapan peran di Service Layer
                if (isset($data['role'])) {
                    $user->assignRole($data['role']);
                }

                return $user->load('roles'); // Pastikan role dimuat sebelum dikembalikan
            } catch (Throwable $e) {
                // Tangkap setiap pengecualian dan ubah menjadi BusinessLogicException
                throw new BusinessLogicException($e->getMessage());
            }
        });
    }

    /**
     * Update user information.
     */
    public function update(int $id, array $data): bool
    {
        return DB::transaction(function () use ($id, $data) {
            $user = $this->findById($id); // Bisa melempar BusinessLogicException

            if (isset($data['role']) && $data['role'] === RoleEnum::ADMIN->value) {
                $this->validateAdminLimitForUpdate($user);
            }

            // Logika pembaruan peran di Service Layer
            if (isset($data['role'])) {
                $user->syncRoles([$data['role']]);
                // Hapus 'role' dari data agar repository tidak mencoba memprosesnya lagi
                unset($data['role']);
            }

            return $this->userRepository->update($id, $data);
        });
    }

    /**
     * Delete a user.
     */
    public function delete(int $id): bool
    {
        return DB::transaction(function () use ($id) {
            $user = $this->findById($id); // Bisa melempar BusinessLogicException

            $this->validateUserForDeletion($user);
            $this->validateNotDeletingSelf($user);

            return $this->userRepository->delete($id);
        });
    }

    /**
     * Change user status.
     */
    public function changeStatus(int $id, bool $status): bool
    {
        return DB::transaction(function () use ($id, $status) {
            $user = $this->findById($id); // Bisa melempar BusinessLogicException

            $this->validateNotChangingOwnStatus($user);

            if (!$status) { // If deactivating user
                $this->validateDeactivatingLastAdmin($user);
            }

            return $this->userRepository->changeStatus($id, $status);
        });
    }

    /**
     * Validates if adding a new admin user would exceed the limit.
     *
     * @throws BusinessLogicException If the admin user limit is reached.
     */
    protected function validateAdminLimitForCreation(): void
    {
        $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);
        if ($adminCount >= self::MAX_ADMIN_COUNT) {
            throw new BusinessLogicException('Maksimal jumlah pengguna admin (' . self::MAX_ADMIN_COUNT . ') telah tercapai.');
        }
    }

    /**
     * Validates if updating a user to admin role would exceed the limit.
     *
     * @param User $user The user being updated.
     * @throws BusinessLogicException If the admin user limit is reached and the user is not already an admin.
     */
    protected function validateAdminLimitForUpdate(User $user): void
    {
        if ($user->getRoleNames()->first() !== RoleEnum::ADMIN->value) {
            $adminCount = $this->userRepository->countUsersByRole(RoleEnum::ADMIN->value);
            if ($adminCount >= self::MAX_ADMIN_COUNT) {
                throw new BusinessLogicException('Maksimal jumlah pengguna admin (' . self::MAX_ADMIN_COUNT . ') telah tercapai.');
            }
        }
    }

    /**
     * Validates if a user can be deleted.
     *
     * @param User $user The user to be validated for deletion.
     * @throws BusinessLogicException If the user is active.
     */
    protected function validateUserForDeletion(User $user): void
    {
        if ($user->status === UserStatus::Active) {
            throw new BusinessLogicException('Pengguna aktif tidak dapat dihapus! Nonaktifkan terlebih dahulu.');
        }
    }

    /**
     * Validates if the currently authenticated user is trying to delete their own account.
     *
     * @param User $user The user attempting to be deleted.
     * @throws BusinessLogicException If the user is trying to delete their own account.
     */
    protected function validateNotDeletingSelf(User $user): void
    {
        if ($user->id === Auth::id()) {
            throw new BusinessLogicException('Tidak dapat menghapus akun sendiri.');
        }
    }

    /**
     * Validates if the currently authenticated user is trying to change their own status.
     *
     * @param User $user The user whose status is being changed.
     * @throws BusinessLogicException If the user is trying to change their own status.
     */
    protected function validateNotChangingOwnStatus(User $user): void
    {
        if ($user->id === Auth::id()) {
            throw new BusinessLogicException('Tidak dapat mengubah status akun sendiri.');
        }
    }

    /**
     * Validates if deactivating an admin user would leave no active admins.
     *
     * @param User $user The admin user being deactivated.
     * @throws BusinessLogicException If deactivating this admin would leave less than one active admin.
     */
    protected function validateDeactivatingLastAdmin(User $user): void
    {
        if ($user->getRoleNames()->first() === RoleEnum::ADMIN->value) {
            $activeAdminCount = $this->userRepository->countActiveUsersByRole(RoleEnum::ADMIN->value);
            if ($activeAdminCount <= 1) {
                throw new BusinessLogicException('Tidak dapat menonaktifkan akun admin terakhir.');
            }
        }
    }
}
