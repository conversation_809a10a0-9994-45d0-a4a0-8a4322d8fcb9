<?php

namespace App\Console\Commands;

use App\Services\ServiceProviderValidationService;
use Illuminate\Console\Command;

class ValidateServiceBindings extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:validate-bindings 
                            {--detailed : Show detailed binding information}
                            {--test-functionality : Test repository functionality}
                            {--json : Output results in JSON format}';

    /**
     * The console command description.
     */
    protected $description = 'Validate service provider bindings and dependency injection';

    /**
     * Service provider validation service.
     */
    protected ServiceProviderValidationService $validationService;

    /**
     * Create a new command instance.
     */
    public function __construct(ServiceProviderValidationService $validationService)
    {
        parent::__construct();
        $this->validationService = $validationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('🔍 Validating Service Provider Bindings...');
        $this->newLine();

        // Run comprehensive validation
        $results = $this->validationService->runComprehensiveValidation();

        if ($this->option('json')) {
            $this->line(json_encode($results, JSON_PRETTY_PRINT));
            return Command::SUCCESS;
        }

        // Display summary
        $this->displaySummary($results['summary']);
        $this->newLine();

        // Display repository bindings
        $this->displayRepositoryBindings($results['repository_bindings']);
        $this->newLine();

        // Display service resolution
        $this->displayServiceResolution($results['service_resolution']);
        $this->newLine();

        // Display controller dependencies
        $this->displayControllerDependencies($results['controller_dependencies']);
        $this->newLine();

        // Show detailed information if requested
        if ($this->option('detailed')) {
            $this->displayDetailedBindings();
            $this->newLine();
        }

        // Test functionality if requested
        if ($this->option('test-functionality')) {
            $this->testRepositoryFunctionality();
            $this->newLine();
        }

        // Final status
        $totalErrors = $results['summary']['failed_validations'];
        if ($totalErrors > 0) {
            $this->error("❌ Validation completed with {$totalErrors} errors. Check the logs for details.");
            return Command::FAILURE;
        } else {
            $this->info('✅ All service provider bindings are valid!');
            return Command::SUCCESS;
        }
    }

    /**
     * Display validation summary.
     */
    private function displaySummary(array $summary): void
    {
        $this->info('📊 Validation Summary');
        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Validations', $summary['total_validations']],
                ['Successful', $summary['successful_validations']],
                ['Failed', $summary['failed_validations']],
                ['Warnings', $summary['warning_validations']],
                ['Execution Time', $summary['execution_time_ms'] . ' ms'],
            ]
        );
    }

    /**
     * Display repository binding results.
     */
    private function displayRepositoryBindings(array $bindings): void
    {
        $this->info('🔗 Repository Bindings');
        
        $tableData = [];
        foreach ($bindings as $interface => $result) {
            $status = $this->getStatusIcon($result['status']);
            $implementation = $result['implementation'] ? class_basename($result['implementation']) : 'N/A';
            
            $tableData[] = [
                $status,
                class_basename($interface),
                $implementation,
                $result['message']
            ];
        }

        $this->table(['Status', 'Interface', 'Implementation', 'Message'], $tableData);
    }

    /**
     * Display service resolution results.
     */
    private function displayServiceResolution(array $services): void
    {
        $this->info('⚙️ Service Resolution');
        
        $tableData = [];
        foreach ($services as $service => $result) {
            $status = $this->getStatusIcon($result['status']);
            $instance = $result['instance'] ? class_basename($result['instance']) : 'N/A';
            
            $tableData[] = [
                $status,
                class_basename($service),
                $instance,
                $result['message']
            ];
        }

        $this->table(['Status', 'Service', 'Instance', 'Message'], $tableData);
    }

    /**
     * Display controller dependency results.
     */
    private function displayControllerDependencies(array $controllers): void
    {
        $this->info('🎮 Controller Dependencies');
        
        $tableData = [];
        foreach ($controllers as $controller => $result) {
            $status = $this->getStatusIcon($result['status']);
            $instance = $result['instance'] ? class_basename($result['instance']) : 'N/A';
            
            $tableData[] = [
                $status,
                class_basename($controller),
                $instance,
                $result['message']
            ];
        }

        $this->table(['Status', 'Controller', 'Instance', 'Message'], $tableData);
    }

    /**
     * Display detailed binding information.
     */
    private function displayDetailedBindings(): void
    {
        $this->info('🔍 Detailed Binding Information');
        
        $bindings = $this->validationService->getBindingDetails();
        $interfaceImplementations = $this->validationService->validateInterfaceImplementations();
        
        $tableData = [];
        foreach ($bindings as $interface => $details) {
            $implResult = $interfaceImplementations[$interface] ?? ['implements_interface' => false];
            
            $tableData[] = [
                class_basename($interface),
                $details['is_bound'] ? '✅' : '❌',
                $details['is_singleton'] ? '✅' : '❌',
                $implResult['implements_interface'] ? '✅' : '❌',
                $details['concrete'] ? class_basename($details['concrete']) : 'N/A'
            ];
        }

        $this->table(
            ['Interface', 'Bound', 'Singleton', 'Implements', 'Concrete'],
            $tableData
        );
    }

    /**
     * Test repository functionality.
     */
    private function testRepositoryFunctionality(): void
    {
        $this->info('🧪 Testing Repository Functionality');
        
        $results = $this->validationService->testRepositoryFunctionality();
        
        $tableData = [];
        foreach ($results as $interface => $result) {
            $status = $this->getStatusIcon($result['status']);
            $methodTests = $result['method_tests'] ?? [];
            $passedTests = count(array_filter($methodTests));
            $totalTests = count($methodTests);
            
            $tableData[] = [
                $status,
                class_basename($interface),
                "{$passedTests}/{$totalTests}",
                $result['total_methods'],
                $result['message']
            ];
        }

        $this->table(
            ['Status', 'Repository', 'Method Tests', 'Total Methods', 'Message'],
            $tableData
        );
    }

    /**
     * Get status icon for display.
     */
    private function getStatusIcon(string $status): string
    {
        return match ($status) {
            'success' => '✅',
            'error' => '❌',
            'warning' => '⚠️',
            default => '❓'
        };
    }
}
