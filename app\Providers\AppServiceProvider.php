<?php

namespace App\Providers;

use App\Repositories\Contracts\AcademicYearRepositoryInterface;
use App\Repositories\Contracts\AttendanceRepositoryInterface;
use App\Repositories\Contracts\ClassroomRepositoryInterface;
use App\Repositories\Contracts\ClassScheduleRepositoryInterface;
use App\Repositories\Contracts\LeaveRequestRepositoryInterface;
use App\Repositories\Contracts\LessonHourRepositoryInterface;
use App\Repositories\Contracts\ProgramRepositoryInterface;
use App\Repositories\Contracts\ShiftRepositoryInterface;
use App\Repositories\Contracts\StaffRepositoryInterface;
use App\Repositories\Contracts\StudentRepositoryInterface;
use App\Repositories\Contracts\SubjectRepositoryInterface;
use App\Repositories\Contracts\TeacherAssignmentRepositoryInterface;
use App\Repositories\Contracts\TeacherRepositoryInterface;
use App\Repositories\Contracts\UserRepositoryInterface;
use App\Repositories\Eloquent\AcademicYearRepository;
use App\Repositories\Eloquent\AttendanceRepository;
use App\Repositories\Eloquent\ClassroomRepository;
use App\Repositories\Eloquent\ClassScheduleRepository;
use App\Repositories\Eloquent\LeaveRequestRepository;
use App\Repositories\Eloquent\LessonHourRepository;
use App\Repositories\Eloquent\ProgramRepository;
use App\Repositories\Eloquent\ShiftRepository;
use App\Repositories\Eloquent\StaffRepository;
use App\Repositories\Eloquent\StudentRepository;
use App\Repositories\Eloquent\SubjectRepository;
use App\Repositories\Eloquent\TeacherAssignmentRepository;
use App\Repositories\Eloquent\TeacherRepository;
use App\Repositories\Eloquent\UserRepository;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $bindings = [
            UserRepositoryInterface::class => UserRepository::class,
            TeacherRepositoryInterface::class => TeacherRepository::class,
            StudentRepositoryInterface::class => StudentRepository::class,
            StaffRepositoryInterface::class => StaffRepository::class,
            ProgramRepositoryInterface::class => ProgramRepository::class,
            AcademicYearRepositoryInterface::class => AcademicYearRepository::class,
            ClassroomRepositoryInterface::class => ClassroomRepository::class,
            SubjectRepositoryInterface::class => SubjectRepository::class,
            LessonHourRepositoryInterface::class => LessonHourRepository::class,
            TeacherAssignmentRepositoryInterface::class => TeacherAssignmentRepository::class,
            ClassScheduleRepositoryInterface::class => ClassScheduleRepository::class,
            ShiftRepositoryInterface::class => ShiftRepository::class,
            AttendanceRepositoryInterface::class => AttendanceRepository::class,
            LeaveRequestRepositoryInterface::class => LeaveRequestRepository::class,
        ];

        foreach ($bindings as $interface => $implementation) {
            $this->app->bind($interface, $implementation);
        }
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }
}
