<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\TeacherAssignmentRepositoryInterface;
use App\Exceptions\TeacherAssignmentException;
use App\Models\TeacherAssignment;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\Log;

class TeacherAssignmentRepository implements TeacherAssignmentRepositoryInterface
{
    /**
     * TeacherAssignment model instance
     */
    private TeacherAssignment $teacherAssignmentModel;

    /**
     * TeacherAssignmentRepository constructor.
     */
    public function __construct(TeacherAssignment $teacherAssignmentModel)
    {
        $this->teacherAssignmentModel = $teacherAssignmentModel;
    }

    /**
     * Get all teacher assignments with optional filtering
     */
    public function getAllTeacherAssignments(array $filters): Collection
    {
        $query = $this->teacherAssignmentModel->with(['teacher.user', 'subject', 'classroom', 'academicYear']);
        $query = $this->applyFilters($query, $filters);

        return $query->get();
    }

    /**
     * Get a teacher assignment by ID
     */
    public function getTeacherAssignmentById(int $id): TeacherAssignment
    {
        try {
            return $this->teacherAssignmentModel->with(['teacher.user', 'subject', 'classroom', 'academicYear'])->findOrFail($id);
        } catch (ModelNotFoundException $e) {
            throw TeacherAssignmentException::notFound("Penugasan guru dengan ID {$id} tidak ditemukan");
        }
    }

    /**
     * Create a new teacher assignment
     */
    public function createTeacherAssignment(array $data): TeacherAssignment
    {
        try {
            return $this->teacherAssignmentModel->create([
                'teacher_id' => $data['teacher_id'],
                'subject_id' => $data['subject_id'] ?? null,
                'classroom_id' => $data['classroom_id'],
                'academic_year_id' => $data['academic_year_id'],
                'is_homeroom_teacher' => $data['is_homeroom_teacher'] ?? false,
            ]);
        } catch (\Exception $e) {
            Log::error('Create teacher assignment failed: ' . $e->getMessage());
            throw new \Exception('Gagal menyimpan penugasan guru: ' . $e->getMessage());
        }
    }

    /**
     * Update an existing teacher assignment
     */
    public function updateTeacherAssignment(int $id, array $data): bool
    {
        try {
            $teacherAssignment = $this->getTeacherAssignmentById($id);

            $updateData = [
                'teacher_id' => $data['teacher_id'] ?? $teacherAssignment->teacher_id,
                'subject_id' => $data['subject_id'] ?? $teacherAssignment->subject_id,
                'classroom_id' => $data['classroom_id'] ?? $teacherAssignment->classroom_id,
                'academic_year_id' => $data['academic_year_id'] ?? $teacherAssignment->academic_year_id,
                'is_homeroom_teacher' => $data['is_homeroom_teacher'] ?? $teacherAssignment->is_homeroom_teacher,
            ];

            // Check if data has actually changed
            if (
                $teacherAssignment->teacher_id === $updateData['teacher_id'] &&
                $teacherAssignment->subject_id === $updateData['subject_id'] &&
                $teacherAssignment->classroom_id === $updateData['classroom_id'] &&
                $teacherAssignment->academic_year_id === $updateData['academic_year_id'] &&
                $teacherAssignment->is_homeroom_teacher === $updateData['is_homeroom_teacher']
            ) {
                return false;
            }

            $teacherAssignment->fill($updateData);
            return $teacherAssignment->save();
        } catch (\Exception $e) {
            Log::error('Update teacher assignment failed: ' . $e->getMessage());
            throw new \Exception('Gagal memperbarui penugasan guru: ' . $e->getMessage());
        }
    }

    /**
     * Delete a teacher assignment
     */
    public function deleteTeacherAssignment(int $id): bool
    {
        try {
            $teacherAssignment = $this->getTeacherAssignmentById($id);
            return $teacherAssignment->delete();
        } catch (\Exception $e) {
            Log::error('Delete teacher assignment failed: ' . $e->getMessage());
            throw new \Exception('Gagal menghapus penugasan guru: ' . $e->getMessage());
        }
    }

    /**
     * Get teacher assignments by teacher ID
     */
    public function getTeacherAssignmentsByTeacherId(int $teacherId, array $filters = []): Collection
    {
        $query = $this->teacherAssignmentModel
            ->with(['teacher.user', 'subject', 'classroom', 'academicYear'])
            ->where('teacher_id', $teacherId);

        $query = $this->applyFilters($query, $filters);

        return $query->get();
    }

    /**
     * Get teacher assignments by classroom ID
     */
    public function getTeacherAssignmentsByClassroomId(int $classroomId, array $filters = []): Collection
    {
        $query = $this->teacherAssignmentModel
            ->with(['teacher.user', 'subject', 'classroom', 'academicYear'])
            ->where('classroom_id', $classroomId);

        $query = $this->applyFilters($query, $filters);

        return $query->get();
    }

    /**
     * Get homeroom teacher by classroom ID and academic year ID
     */
    public function getHomeroomTeacherByClassroomId(int $classroomId, int $academicYearId): ?TeacherAssignment
    {
        return $this->teacherAssignmentModel
            ->with(['teacher.user', 'classroom', 'academicYear'])
            ->where('classroom_id', $classroomId)
            ->where('academic_year_id', $academicYearId)
            ->where('is_homeroom_teacher', true)
            ->first();
    }

    /**
     * Check if homeroom teacher exists for classroom and academic year
     */
    public function hasHomeroomTeacher(int $classroomId, int $academicYearId, ?int $excludeId = null): bool
    {
        $query = $this->teacherAssignmentModel
            ->where('classroom_id', $classroomId)
            ->where('academic_year_id', $academicYearId)
            ->where('is_homeroom_teacher', true);

        if ($excludeId !== null) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if teacher assignment has duplicate
     */
    public function hasDuplicateAssignment(array $data, ?int $excludeId = null): bool
    {
        $query = $this->teacherAssignmentModel
            ->where('teacher_id', $data['teacher_id'])
            ->where('subject_id', $data['subject_id'])
            ->where('classroom_id', $data['classroom_id'])
            ->where('academic_year_id', $data['academic_year_id']);

        if ($excludeId !== null) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if teacher assignment has active class schedules
     */
    public function hasActiveClassSchedules(int $id): bool
    {
        return $this->teacherAssignmentModel
            ->where('id', $id)
            ->whereHas('classSchedules', function ($query) {
                $query->where('status', 'active');
            })
            ->exists();
    }

    /**
     * Apply filters to query
     */
    private function applyFilters($query, array $filters)
    {
        // Filter by teacher ID
        if (!empty($filters['teacher_id'])) {
            $query->where('teacher_id', $filters['teacher_id']);
        }

        // Filter by subject ID
        if (!empty($filters['subject_id'])) {
            $query->where('subject_id', $filters['subject_id']);
        }

        // Filter by classroom ID
        if (!empty($filters['classroom_id'])) {
            $query->where('classroom_id', $filters['classroom_id']);
        }

        // Filter by academic year ID
        if (!empty($filters['academic_year_id'])) {
            $query->where('academic_year_id', $filters['academic_year_id']);
        }

        // Filter by homeroom teacher status
        if (isset($filters['is_homeroom_teacher'])) {
            $query->where('is_homeroom_teacher', $filters['is_homeroom_teacher']);
        }

        // Global search functionality
        if (!empty($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                // Search in teacher names
                $q->whereHas('teacher.user', function ($teacherQuery) use ($search) {
                    $teacherQuery->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%");
                })
                    // Search in subject names
                    ->orWhereHas('subject', function ($subjectQuery) use ($search) {
                        $subjectQuery->where('name', 'like', "%{$search}%");
                    })
                    // Search in classroom names
                    ->orWhereHas('classroom', function ($classroomQuery) use ($search) {
                        $classroomQuery->where('name', 'like', "%{$search}%");
                    })
                    // Search in academic year names
                    ->orWhereHas('academicYear', function ($academicYearQuery) use ($search) {
                        $academicYearQuery->where('name', 'like', "%{$search}%");
                    });
            });
        }

        // Order by
        if (!empty($filters['order_by'])) {
            $direction = !empty($filters['order_direction']) ? $filters['order_direction'] : 'asc';
            $query->orderBy($filters['order_by'], $direction);
        } else {
            $query->orderBy('id', 'desc');
        }

        return $query;
    }
}

