<?php

namespace App\Repositories\Eloquent;

use App\Repositories\Contracts\UserRepositoryInterface;
use App\Enums\UserStatus;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class UserRepository implements UserRepositoryInterface
{
    protected User $model;

    public function __construct(User $model)
    {
        $this->model = $model;
    }

    /**
     * Get all users with optional filtering.
     */
    public function getAll(array $filters = []): Collection
    {
        $query = $this->model->with('roles');

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['role'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->where('name', $filters['role']);
            });
        }

        if (isset($filters['role_in']) && is_array($filters['role_in'])) {
            $query->whereHas('roles', function ($q) use ($filters) {
                $q->whereIn('name', $filters['role_in']);
            });
        }

        if (isset($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', $searchTerm)
                    ->orWhere('email', 'like', $searchTerm)
                    ->orWhere('username', 'like', $searchTerm);
            });
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * Find a user by ID.
     * Throws ModelNotFoundException if user is not found.
     */
    public function findById(int $id): User
    {
        return $this->model->with('roles')->findOrFail($id);
    }

    /**
     * Create a new user.
     * Does NOT handle role assignment.
     */
    public function create(array $data): User
    {
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        // Hapus 'role' dari data agar tidak disimpan sebagai kolom langsung di tabel users
        unset($data['role']);

        $user = $this->model->create($data);
        return $user;
    }

    /**
     * Update an existing user.
     * Does NOT handle role assignment.
     */
    public function update(int $id, array $data): bool
    {
        $user = $this->findById($id);

        if (isset($data['password']) && !empty($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        } else {
            unset($data['password']);
        }
        // 'role' seharusnya sudah di-unset di Service Layer jika ada

        return $user->update($data);
    }

    /**
     * Delete a user.
     */
    public function delete(int $id): bool
    {
        $user = $this->findById($id);
        return $user->delete();
    }

    /**
     * Count users by role.
     */
    public function countUsersByRole(string $role): int
    {
        return $this->model->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        })->count();
    }

    /**
     * Count active users by role.
     */
    public function countActiveUsersByRole(string $role): int
    {
        return $this->model->whereHas('roles', function ($q) use ($role) {
            $q->where('name', $role);
        })->where('status', UserStatus::Active->value)->count();
    }

    /**
     * Change user status directly in the database.
     */
    public function changeStatus(int $id, bool $status): bool
    {
        $user = $this->findById($id);
        return $user->update([
            'status' => $status ? UserStatus::Active->value : UserStatus::Inactive->value
        ]);
    }
}
